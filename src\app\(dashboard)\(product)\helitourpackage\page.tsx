"use client"
import { PackagesList } from "@/components/package/package-list"

const mockPackages = [
  {
    id: 1,
    packageName: "Langtang Trek: Cost, Itinerary, Elevation, Difficulty & Route Map",
    destination: "Nepal",
    region: "Langtang Region",
    activity: "Trekking",
    slug: "langtang-trek-cost-itinerary-route-map",
    published: true,
  },
  {
    id: 2,
    packageName: "Annapurna Circuit Trek with Tilicho Lake",
    destination: "Nepal",
    region: "Annapurna Region",
    activity: "Trekking",
    slug: "tilicho-lake-trek",
    published: true,
  },
  {
    id: 3,
    packageName: "Annapurna Circuit Trek",
    destination: "Nepal",
    region: "Annapurna Region",
    activity: "Trekking",
    slug: "annapurna-circuit-trek",
    published: true,
  },
]

export default function HeliTourPackages() {
  const handleEdit = (id: number) => {
    console.log("Edit trekking package:", id)
  }

  const handleDelete = (id: number) => {
    console.log("Delete trekking package:", id)
  }

  return (
    <PackagesList
      title="All HeliTour Packages"
      packages={mockPackages}
      createUrl="/trekkingpackage/create"
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  )
}
