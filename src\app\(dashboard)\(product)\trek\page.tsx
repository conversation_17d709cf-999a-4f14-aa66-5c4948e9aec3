"use client"
import { CategoriesList } from "@/components/category/category-list"

const mockCategories = [
  { id: 1, name: "High Altitude Trekking", slug: "high-altitude-trekking", published: true },
  { id: 2, name: "Tea House Trekking", slug: "tea-house-trekking", published: true },
  { id: 3, name: "Camping Trekking", slug: "camping-trekking", published: false },
  { id: 4, name: "Peak Climbing", slug: "peak-climbing", published: true },
]

export default function TrekkingCategories() {
  const handleEdit = (id: number) => {
    console.log("Edit category:", id)
  }

  const handleDelete = (id: number) => {
    console.log("Delete category:", id)
  }

  return (
    <CategoriesList
      title="All Trekking Categories"
      categories={mockCategories}
      createUrl="/trekking/create"
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  )
}
