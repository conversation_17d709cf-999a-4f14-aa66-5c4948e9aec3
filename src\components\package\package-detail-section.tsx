"use client"

import dynamic from "next/dynamic"
import { <PERSON><PERSON><PERSON>, SetStateAction } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { PackageFormData } from "@/types/package-form"

const CkEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

interface Props {
  formData: PackageFormData
  setFormData: Dispatch<SetStateAction<PackageFormData>>
}

export function PackageCreateSection({ formData, setFormData }: Props) {
  const handlePackageNameChange = (name: string) => {
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "")
    setFormData(prev => ({ ...prev, packageName: name, slug }))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Package Create</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Row 1 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="packageName">Package Name (H1)</Label>
            <Input
              id="packageName"
              value={formData.packageName}
              onChange={e => handlePackageNameChange(e.target.value)}
              placeholder="Enter Package Name"
              required
            />
          </div>
          <div>
            <Label htmlFor="slug">Slug</Label>
            <Input
              id="slug"
              value={formData.slug}
              onChange={e => setFormData(prev => ({ ...prev, slug: e.target.value }))}
              placeholder="Auto-generated from name"
            />
          </div>
        </div>
        {/* Row 2 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="region">Select Region</Label>
            <Select
              value={formData.region}
              onValueChange={v => setFormData(prev => ({ ...prev, region: v }))}
            >
              <SelectTrigger><SelectValue placeholder="-- select one --" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="annapurna">Annapurna Region</SelectItem>
                <SelectItem value="everest">Everest Region</SelectItem>
                <SelectItem value="langtang">Langtang Region</SelectItem>
                <SelectItem value="manaslu">Manaslu Region</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="destination">Select Destination</Label>
            <Select
              value={formData.destination}
              onValueChange={v => setFormData(prev => ({ ...prev, destination: v }))}
            >
              <SelectTrigger><SelectValue placeholder="-- select one --" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="nepal">Nepal</SelectItem>
                <SelectItem value="tibet">Tibet</SelectItem>
                <SelectItem value="bhutan">Bhutan</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        {/* Row 3 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="activity">Select Activity</Label>
            <Select
              value={formData.activity}
              onValueChange={v => setFormData(prev => ({ ...prev, activity: v }))}
            >
              <SelectTrigger><SelectValue placeholder="-- select one --" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="trekking">Trekking</SelectItem>
                <SelectItem value="tour">Tour</SelectItem>
                <SelectItem value="adventure">Adventure</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="accommodation">Accommodation</Label>
            <Input
              id="accommodation"
              value={formData.accommodation}
              onChange={e => setFormData(prev => ({ ...prev, accommodation: e.target.value }))}
              placeholder="Hotel/Lodge/Tea House"
            />
          </div>
        </div>
        {/* Row 4: distance & days/nights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="distance">Distance</Label>
            <Input
              id="distance"
              value={formData.distance}
              onChange={e => setFormData(prev => ({ ...prev, distance: e.target.value }))}
              placeholder="e.g. Lukla–EBC–Lukla (130 km)"
            />
          </div>
          <div>
            <Label htmlFor="daysAndNights">Days & Nights</Label>
            <Input
              id="daysAndNights"
              value={formData.daysAndNights}
              onChange={e => setFormData(prev => ({ ...prev, daysAndNights: e.target.value }))}
              placeholder="e.g. 3N/4D"
            />
          </div>
        </div>
        {/* Row 5 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="maxAltitude">Max Altitude</Label>
            <Input
              id="maxAltitude"
              value={formData.maxAltitude}
              onChange={e => setFormData(prev => ({ ...prev, maxAltitude: e.target.value }))}
              placeholder="e.g. 5 555 m"
            />
          </div>
          <div>
            <Label htmlFor="mealsInclude">Meals Include</Label>
            <Input
              id="mealsInclude"
              value={formData.mealsInclude}
              onChange={e => setFormData(prev => ({ ...prev, mealsInclude: e.target.value }))}
              placeholder="e.g. BLD"
            />
          </div>
        </div>
        {/* Row 6 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="groupSize">Group Size</Label>
            <Input
              id="groupSize"
              value={formData.groupSize}
              onChange={e => setFormData(prev => ({ ...prev, groupSize: e.target.value }))}
              placeholder="e.g. 2–10"
            />
          </div>
          <div>
            <Label htmlFor="natureOfTrek">Nature of Trek</Label>
            <Input
              id="natureOfTrek"
              value={formData.natureOfTrek}
              onChange={e => setFormData(prev => ({ ...prev, natureOfTrek: e.target.value }))}
              placeholder="e.g. Lodge-to-Lodge"
            />
          </div>
        </div>
        {/* Row 7 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="bestSeason">Best Season</Label>
            <Input
              id="bestSeason"
              value={formData.bestSeason}
              onChange={e => setFormData(prev => ({ ...prev, bestSeason: e.target.value }))}
              placeholder="e.g. Mar–May, Sept–Nov"
            />
          </div>
          <div>
            <Label htmlFor="tripCode">Trip Code</Label>
            <Input
              id="tripCode"
              value={formData.tripCode}
              onChange={e => setFormData(prev => ({ ...prev, tripCode: e.target.value }))}
              placeholder="e.g. DWTK01"
            />
          </div>
        </div>
        {/* Row 8 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="price">Price</Label>
            <Input
              id="price"
              value={formData.price}
              onChange={e => setFormData(prev => ({ ...prev, price: e.target.value }))}
              placeholder="e.g. 2000"
            />
          </div>
          <div>
            <Label htmlFor="discountPrice">Discount Price</Label>
            <Input
              id="discountPrice"
              value={formData.discountPrice}
              onChange={e => setFormData(prev => ({ ...prev, discountPrice: e.target.value }))}
              placeholder="e.g. 1500"
            />
          </div>
        </div>
        {/* Row 9 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="activityPerDay">Activity/Day</Label>
            <Input
              id="activityPerDay"
              value={formData.activityPerDay}
              onChange={e => setFormData(prev => ({ ...prev, activityPerDay: e.target.value }))}
              placeholder="e.g. 4–6 hrs walking"
            />
          </div>
          <div>
            <Label htmlFor="transportation">Transportation</Label>
            <Input
              id="transportation"
              value={formData.transportation}
              onChange={e => setFormData(prev => ({ ...prev, transportation: e.target.value }))}
              placeholder="e.g. KTM–Lukla flight + vehicle"
            />
          </div>
        </div>
        {/* Row 10 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="grade">Grade</Label>
            <Input
              id="grade"
              value={formData.grade}
              onChange={e => setFormData(prev => ({ ...prev, grade: e.target.value }))}
              placeholder="e.g. Challenging"
            />
          </div>
          <div>
            <Label htmlFor="imageAltTag">Image Alt Tag</Label>
            <Input
              id="imageAltTag"
              value={formData.imageAltTag}
              onChange={e => setFormData(prev => ({ ...prev, imageAltTag: e.target.value }))}
              placeholder="Describe image"
            />
          </div>
        </div>
        {/* Row 11: files */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <Label htmlFor="image">Image</Label>
            <Input
              id="image"
              type="file"
              accept="image/*"
              onChange={e => setFormData(prev => ({ ...prev, image: e.target.files?.[0]||null }))}
            />
          </div>
          <div>
            <Label htmlFor="packagePdf">Package PDF</Label>
            <Input
              id="packagePdf"
              type="file"
              accept=".pdf"
              onChange={e => setFormData(prev => ({ ...prev, packagePdf: e.target.files?.[0]||null }))}
            />
          </div>
          <div>
            <Label htmlFor="thumbnailImage">Thumbnail</Label>
            <Input
              id="thumbnailImage"
              type="file"
              accept="image/*"
              onChange={e => setFormData(prev => ({ ...prev, thumbnailImage: e.target.files?.[0]||null }))}
            />
          </div>
        </div>
        {/* Row 12 */}
        <div>
          <Label htmlFor="bookingLink">Booking Link</Label>
          <Input
            id="bookingLink"
            value={formData.bookingLink}
            onChange={e => setFormData(prev => ({ ...prev, bookingLink: e.target.value }))}
            placeholder="https://"
          />
        </div>
        {/* Description */}
        <div>
          <Label htmlFor="description">Description</Label>
          <CkEditor
            value={formData.description}
            onChange={e => setFormData(prev => ({ ...prev, description: e }))}
          />
        </div>
        {/* Checkboxes */}
        <div className="flex flex-wrap gap-6">
          {[
            ["published","Published"],
            ["tripOfTheMonth","Trip of the Month"],
            ["popularTours","Popular Tours"],
            ["shortTrek","Short Trek"],
          ].map(([key,label]) => (
            <div className="flex items-center space-x-2" key={key}>
              <Checkbox
                id={key}
                checked={formData[key as keyof Pick<PackageFormData, 'published' | 'tripOfTheMonth' | 'popularTours' | 'shortTrek'>]}
                onCheckedChange={v => setFormData(prev => ({ ...prev, [key]: !!v }))}
              />
              <Label htmlFor={key}>{label}</Label>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
