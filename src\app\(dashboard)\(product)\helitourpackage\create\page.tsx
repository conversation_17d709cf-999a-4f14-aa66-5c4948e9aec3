"use client"
import { PackageForm } from "@/components/package/package-form"
import { PackageFormData } from "@/types/package-form"
import { useRouter } from "next/navigation"

export default function HeliTourPackage() {
  const router = useRouter()

  const handleSubmit = (data: PackageFormData) => {
    console.log("Trekking package data:", data)

    router.push("/trekkingpackage")
  }

  return <PackageForm title="Heli Tour Package Create" onSubmit={handleSubmit} backUrl="/helitourpackage" />
}
