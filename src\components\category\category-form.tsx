"use client"
import { useState } from "react"
import type React from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import dynamic from "next/dynamic"

const CkEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false });

interface CategoryFormProps {
  title: string
  onSubmit: (data: CategoryFormData) => void
  initialData?: Partial<CategoryFormData>
  backUrl?: string
}

export interface CategoryFormData {
  name: string
  slug: string
  imageAltTag: string
  image: File | null
  description: string
  published: boolean
}

export function CategoryForm({ title, onSubmit, initialData, backUrl }: CategoryFormProps) {
  const router = useRouter()
  const [formData, setFormData] = useState<CategoryFormData>({
    name: initialData?.name || "",
    slug: initialData?.slug || "",
    imageAltTag: initialData?.imageAltTag || "",
    image: initialData?.image || null,
    description: initialData?.description || "",
    published: initialData?.published || false,
  })

  const handleNameChange = (name: string) => {
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "")
    setFormData((prev) => ({ ...prev, name, slug }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">{title}</h1>
        {backUrl && (
          <Button variant="outline" onClick={() => router.push(backUrl)}>
            Back to List
          </Button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="Enter Category Name"
              required
            />
          </div>

          <div>
            <Label htmlFor="slug">Slug</Label>
            <Input
              id="slug"
              value={formData.slug}
              onChange={(e) => setFormData((prev) => ({ ...prev, slug: e.target.value }))}
              placeholder="Enter Category Slug"
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="imageAltTag">Image Alt Tag</Label>
            <Input
              id="imageAltTag"
              value={formData.imageAltTag}
              onChange={(e) => setFormData((prev) => ({ ...prev, imageAltTag: e.target.value }))}
              placeholder="Enter Alt Tag Details"
            />
          </div>

          <div>
            <Label htmlFor="image">Image</Label>
            <Input
              id="image"
              type="file"
              accept="image/*"
              onChange={(e) => setFormData((prev) => ({ ...prev, image: e.target.files?.[0] || null }))}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          {/* <RichTextEditor
            value={formData.description}
            onChange={(description) => setFormData((prev) => ({ ...prev, description }))}
            placeholder="Enter description..."
          /> */}

          <CkEditor
            value={formData.description}
            onChange={(e) => {
              setFormData({
                ...formData,
                description: e,
              });
            }}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="published"
            checked={formData.published}
            onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, published: !!checked }))}
          />
          <Label htmlFor="published">Publish</Label>
        </div>

        <Button type="submit" className="bg-gray-600 hover:bg-gray-700">
          Submit
        </Button>
      </form>
    </div>
  )
}
