"use client"
import { PackageForm } from "@/components/package/package-form"
import { PackageFormData } from "@/types/package-form"
import { useRouter } from "next/navigation"

export default function CreateNaturePackage() {
  const router = useRouter()

  const handleSubmit = (data: PackageFormData) => {
    console.log("Trekking package data:", data)

    router.push("/naturepackage")
  }

  return <PackageForm title="Nature Package Create" onSubmit={handleSubmit} backUrl="/naturepackage" />
}
