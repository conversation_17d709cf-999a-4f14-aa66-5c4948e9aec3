"use client"

import { CategoriesList } from "@/components/category/category-list"

const mockCategories = [
  { id: 1, name: "Cultural Tours", slug: "cultural-tours", published: true },
  { id: 2, name: "Heritage Tours", slug: "heritage-tours", published: true },
  { id: 3, name: "City Tours", slug: "city-tours", published: false },
  { id: 4, name: "Religious Tours", slug: "religious-tours", published: true },
]

export default function TourCategories() {
  const handleEdit = (id: number) => {
    console.log("Edit category:", id)
  }

  const handleDelete = (id: number) => {
    console.log("Delete category:", id)
  }

  return (
    <CategoriesList
      title="All Tour Categories"
      categories={mockCategories}
      createUrl="/tour/create"
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  )
}
