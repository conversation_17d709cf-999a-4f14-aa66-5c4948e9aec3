"use client"

import { PackagesList } from "@/components/package/package-list"

const mockPackages = [
  {
    id: 1,
    packageName: "Kathmandu Valley Tour",
    destination: "Nepal",
    region: "Kathmandu Valley",
    activity: "Tour",
    slug: "kathmandu-valley-tour",
    published: true,
  },
  {
    id: 2,
    packageName: "Pokhara City Tour",
    destination: "Nepal",
    region: "Pokhara",
    activity: "Tour",
    slug: "pokhara-city-tour",
    published: true,
  },
]

export default function TourPackages() {
  const handleEdit = (id: number) => {
    console.log("Edit tour package:", id)
  }

  const handleDelete = (id: number) => {
    console.log("Delete tour package:", id)
  }

  return (
    <PackagesList
      title="All Tour Packages"
      packages={mockPackages}
      createUrl="/tourpackage/create"
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  )
}
