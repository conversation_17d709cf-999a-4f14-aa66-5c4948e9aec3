"use client"

import { CategoriesList } from "@/components/category/category-list"

const mockCategories = [
  { id: 1, name: "Rock Climbing", slug: "rock-climbing", published: true },
  { id: 2, name: "Bungee Jumping", slug: "bungee-jumping", published: true },
  { id: 3, name: "Paragliding", slug: "paragliding", published: false },
  { id: 4, name: "White Water Rafting", slug: "white-water-rafting", published: true },
]

export default function AdventureCategories() {
  const handleEdit = (id: number) => {
    console.log("Edit category:", id)
  }

  const handleDelete = (id: number) => {
    console.log("Delete category:", id)
  }

  return (
    <CategoriesList
      title="All Adventure Categories"
      categories={mockCategories}
      createUrl="/adventure/create"
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  )
}
