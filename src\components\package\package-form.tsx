"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { PackageCreateSection } from "./package-detail-section"
import { SeoDetailsSection } from "./seo-detail-section"
import { SchemaDetailsSection } from "./seo-schema-detail-section"
import type { PackageFormData, SeoFields } from "@/types/package-form"

interface Props {
  title: string
  onSubmit: (data: PackageFormData & SeoFields & { schema: string }) => void
  initialData?: Partial<PackageFormData & SeoFields & { schema: string }>
  backUrl?: string
}

export function PackageForm({ title, onSubmit, initialData, backUrl }: Props) {
  const router = useRouter()
  const [formData, setFormData] = useState<PackageFormData>({
    packageName: "",
    slug: "",
    region: "",
    destination: "",
    activity: "",
    accommodation: "",
    maxAltitude: "",
    groupSize: "",
    bestSeason: "",
    price: "",
    discountPrice: "",
    activityPerDay: "",
    grade: "",
    image: null,
    packagePdf: null,
    thumbnailImage: null,
    bookingLink: "",
    description: "",
    distance: "",
    daysAndNights: "",
    mealsInclude: "",
    natureOfTrek: "",
    tripCode: "",
    transportation: "",
    imageAltTag: "",
    published: false,
    tripOfTheMonth: false,
    popularTours: false,
    shortTrek: false,
    ...initialData,
  })
  const [seo, setSeo] = useState<SeoFields>({
    metaTitle: "",
    metaDescription: "",
    metaKeywords: "",
    canonicalUrl: "",
    ...initialData,
  })
  const [schema, setSchema] = useState(initialData?.schema || "")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({ ...formData, ...seo, schema })
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{title}</h1>
        {backUrl && <Button variant="outline" onClick={() => router.push(backUrl)}>Back</Button>}
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <PackageCreateSection formData={formData} setFormData={setFormData} />
        <SeoDetailsSection formData={seo} setFormData={setSeo} />
        <SchemaDetailsSection schema={schema} setSchema={setSchema} />
        <Button type="submit" className="bg-brand hover:bg-brand/80">
          Submit
        </Button>
      </form>
    </div>
  )
}
