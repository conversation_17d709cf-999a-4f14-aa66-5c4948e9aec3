"use client"
import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Edit, Trash2, ImageIcon } from "lucide-react"

interface Package {
  id: number
  packageName: string
  destination: string
  region: string
  activity: string
  slug: string
  published: boolean
}

interface PackagesListProps {
  title: string
  packages: Package[]
  createUrl: string
  onEdit: (id: number) => void
  onDelete: (id: number) => void
}

export function PackagesList({ title, packages, createUrl, onEdit, onDelete }: PackagesListProps) {
  const [search, setSearch] = useState("")
  const [entriesPerPage, setEntriesPerPage] = useState("25")
  const [currentPage, setCurrentPage] = useState(1)

  const filteredPackages = packages.filter((pkg) => pkg.packageName.toLowerCase().includes(search.toLowerCase()))

  const totalPages = Math.ceil(filteredPackages.length / Number.parseInt(entriesPerPage))
  const startIndex = (currentPage - 1) * Number.parseInt(entriesPerPage)
  const paginatedPackages = filteredPackages.slice(startIndex, startIndex + Number.parseInt(entriesPerPage))

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{title}</h1>
        <Link href={createUrl}>
          <Button className="bg-cyan-500 hover:bg-cyan-600">Add Package</Button>
        </Link>
      </div>

      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <span>Show</span>
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span>entries</span>
        </div>

        <div className="flex items-center gap-2">
          <span>Search:</span>
          <Input
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-64"
            placeholder="Search packages..."
          />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Package Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Destination
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Region</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Activity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slug</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Published
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Options
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedPackages.map((pkg, index) => (
              <tr key={pkg.id} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{startIndex + index + 1}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pkg.packageName}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pkg.destination}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pkg.region}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pkg.activity}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pkg.slug}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      pkg.published ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                    }`}
                  >
                    {pkg.published ? "Published" : "Unpublished"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onEdit(pkg.id)}
                      className="text-green-600 border-green-600 hover:bg-green-50"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onDelete(pkg.id)}
                      className="text-red-600 border-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-cyan-600 border-cyan-600 hover:bg-cyan-50 bg-transparent"
                    >
                      <ImageIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-gray-700">
          Showing {startIndex + 1} to {Math.min(startIndex + Number.parseInt(entriesPerPage), filteredPackages.length)}{" "}
          of {filteredPackages.length} entries
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <Button variant="outline" className="bg-blue-500 text-white">
            {currentPage}
          </Button>
          <Button
            variant="outline"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
