"use client"
import { PackageForm } from "@/components/package/package-form"
import { PackageFormData } from "@/types/package-form"
import { useRouter } from "next/navigation"

export default function CreateTourPackage() {
  const router = useRouter()

  const handleSubmit = (data: PackageFormData) => {
    console.log("Tour package data:", data)

    router.push("/tourpackage")
  }

  return <PackageForm title="Tour Package Create" onSubmit={handleSubmit} backUrl="/tourpackage" />
}
