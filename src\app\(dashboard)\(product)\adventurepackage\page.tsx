"use client"

import { PackagesList } from "@/components/package/package-list"

const mockPackages = [
  {
    id: 1,
    packageName: "Everest Base Camp Helicopter Tour",
    destination: "Nepal",
    region: "Everest Region",
    activity: "Adventure",
    slug: "everest-base-camp-helicopter-tour",
    published: true,
  },
  {
    id: 2,
    packageName: "Annapurna Base Camp Helicopter Tour",
    destination: "Nepal",
    region: "Annapurna Region",
    activity: "Adventure",
    slug: "annapurna-base-camp-helicopter-tour",
    published: true,
  },
]

export default function AdventurePackages() {
  const handleEdit = (id: number) => {
    console.log("Edit adventure package:", id)
  }

  const handleDelete = (id: number) => {
    console.log("Delete adventure package:", id)
  }

  return (
    <PackagesList
      title="All Adventure Packages"
      packages={mockPackages}
      createUrl="/adventurepackage/create"
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  )
}
