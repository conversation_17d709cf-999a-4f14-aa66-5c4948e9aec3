export interface PackageFormData {
  packageName: string
  slug: string
  region: string
  destination: string
  activity: string
  accommodation: string
  maxAltitude: string
  groupSize: string
  bestSeason: string
  price: string
  discountPrice: string
  activityPerDay: string
  grade: string
  image: File | null
  packagePdf: File | null
  thumbnailImage: File | null
  bookingLink: string
  description: string
  distance: string
  daysAndNights: string
  mealsInclude: string
  natureOfTrek: string
  tripCode: string
  transportation: string
  imageAltTag: string
  published: boolean
  tripOfTheMonth: boolean
  popularTours: boolean
  shortTrek: boolean
}

export interface SeoFields {
  metaTitle: string
  metaDescription: string
  metaKeywords: string
  canonicalUrl: string
}
