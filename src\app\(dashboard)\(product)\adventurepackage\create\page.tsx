"use client"
import { PackageForm } from "@/components/package/package-form"
import { PackageFormData } from "@/types/package-form"
import { useRouter } from "next/navigation"

export default function CreateAdventurePackage() {
  const router = useRouter()

  const handleSubmit = (data: PackageFormData) => {
    console.log("Adventure package data:", data)
    // Here you would typically save to database
    // For now, we'll just redirect back to the list
    router.push("/adventurepackage")
  }

  return <PackageForm title="Adventure Package Create" onSubmit={handleSubmit} backUrl="/adventurepackage" />
}
